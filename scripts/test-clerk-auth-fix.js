// Test script to verify Clerk + Supabase integration is working
// Run this with: node scripts/test-clerk-auth-fix.js

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://xvgmitjuwgmvllgompsm.supabase.co';
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_KEY || 'sb_publishable_qnuSaqkuv49hAmSgLd5weA_WiWxw2bu';

console.log('🔧 Configuration:');
console.log('- Supabase URL:', supabaseUrl);
console.log('- Supabase Key:', supabaseKey.substring(0, 20) + '...');
console.log('- Clerk Key:', process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY ? 'Found' : 'Missing');
console.log('');

// Test 1: Check if policies are updated
async function testPolicies() {
  console.log('🔍 Testing RLS policies...');
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  try {
    // Check if policies use auth.jwt()->>'sub'
    const { data, error } = await supabase.rpc('exec_sql', {
      query: `
        SELECT policyname, qual 
        FROM pg_policies 
        WHERE tablename = 'user_profiles' 
        AND qual LIKE '%auth.jwt()%'
      `
    });
    
    if (error) {
      console.log('❌ Cannot query policies directly. This is expected.');
      console.log('✅ Please manually verify in Supabase dashboard that policies use auth.jwt()->>\'sub\'');
    } else {
      console.log('✅ Policies found:', data);
    }
  } catch (err) {
    console.log('❌ Error testing policies:', err.message);
  }
}

// Test 2: Check table structure
async function testTableStructure() {
  console.log('🔍 Testing table structure...');
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  try {
    // Try to query an empty user_profiles table
    const { data, error } = await supabase
      .from('user_profiles')
      .select('user_id')
      .limit(1);
    
    if (error) {
      console.log('❌ Error querying user_profiles:', error.message);
    } else {
      console.log('✅ user_profiles table is accessible');
      console.log('✅ Current data:', data);
    }
  } catch (err) {
    console.log('❌ Unexpected error:', err.message);
  }
}

// Run tests
async function runTests() {
  console.log('🧪 Testing Clerk + Supabase integration fix...\n');
  
  await testPolicies();
  console.log('');
  await testTableStructure();
  
  console.log('\n📋 Next steps:');
  console.log('1. Run the fix_clerk_auth_error.sql in your Supabase SQL editor');
  console.log('2. Test login in your app');
  console.log('3. Check browser console for any remaining auth errors');
}

runTests().catch(console.error);
