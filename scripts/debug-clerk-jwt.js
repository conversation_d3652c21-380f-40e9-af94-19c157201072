// Debug script to test Clerk JWT token validation with Supabase
// This will help identify JWT configuration issues

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_KEY;

console.log('🔍 Debugging Clerk + Supabase JWT Integration...\n');

// Test 1: Check current RLS policies in database
async function checkCurrentPolicies() {
  console.log('📋 Checking current RLS policies...');
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  try {
    // Try to get policy information
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .limit(1);
    
    console.log('✅ Can query user_profiles without auth');
    console.log('Current data count:', data ? data.length : 0);
    
    // Try to insert without auth (should fail with our RLS policies)
    const { error: insertError } = await supabase
      .from('user_profiles')
      .insert({
        user_id: 'test-user-id',
        display_name: 'Test User'
      });
    
    if (insertError) {
      console.log('✅ RLS is working - insert blocked:', insertError.message);
      console.log('Error code:', insertError.code);
    } else {
      console.log('❌ RLS not working - insert succeeded without auth');
    }
    
  } catch (err) {
    console.log('❌ Error testing policies:', err.message);
  }
}

// Test 2: Test with a mock Clerk JWT structure
async function testJWTStructure() {
  console.log('\n🔐 Testing JWT token structure...');
  
  // This is what a Clerk JWT should look like when decoded
  const mockClerkPayload = {
    aud: 'authenticated',
    exp: Math.floor(Date.now() / 1000) + 3600,
    iat: Math.floor(Date.now() / 1000),
    iss: 'https://engaging-haddock-64.clerk.accounts.dev',
    sub: 'user_2abcd1234567890', // This is the Clerk user ID format
    email: '<EMAIL>',
    role: 'authenticated'
  };
  
  console.log('Expected Clerk JWT payload structure:');
  console.log(JSON.stringify(mockClerkPayload, null, 2));
  console.log('\nKey points:');
  console.log('- sub:', mockClerkPayload.sub, '(This should match user_id in database)');
  console.log('- aud:', mockClerkPayload.aud, '(Should be "authenticated")');
  console.log('- role:', mockClerkPayload.role, '(Should be "authenticated")');
}

// Test 3: Check Supabase JWT configuration
async function checkSupabaseJWTConfig() {
  console.log('\n⚙️  Supabase JWT Configuration Check...');
  
  console.log('Supabase URL:', supabaseUrl);
  console.log('Supabase Key prefix:', supabaseKey.substring(0, 15) + '...');
  
  console.log('\n❗ Important JWT Configuration Requirements:');
  console.log('1. Clerk JWT Template "supabase" must be configured in Clerk Dashboard');
  console.log('2. Supabase JWT Secret must match Clerk\'s signing key');
  console.log('3. JWT Template must include: aud="authenticated", role="authenticated"');
  console.log('4. JWT Template sub field must be set to {{user.id}}');
}

// Test 4: Simulate the exact error scenario
async function simulateUserProfileCreation() {
  console.log('\n🧪 Simulating user profile creation scenario...');
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  // This is what your app is trying to do
  const testProfile = {
    user_id: 'user_clerk_test_123', // Clerk-style user ID
    display_name: 'Test User',
    is_public: false,
    allow_garden_sharing: false,
    allow_profile_indexing: false,
    experience_level: 'beginner',
    total_identifications: 0,
    total_diagnoses: 0,
    community_points: 0,
    achievements: []
  };
  
  try {
    // Try the same upsert operation that's failing
    const { data, error } = await supabase
      .from('user_profiles')
      .upsert(testProfile, {
        onConflict: 'user_id',
        ignoreDuplicates: false
      })
      .select()
      .single();
    
    if (error) {
      console.log('❌ Upsert failed (expected without proper JWT):', error.message);
      console.log('Error code:', error.code);
      
      if (error.code === '42501') {
        console.log('🔍 This is the exact error you\'re seeing!');
        console.log('Root cause: RLS policy not recognizing Clerk JWT token');
      }
    } else {
      console.log('✅ Upsert succeeded:', data);
    }
  } catch (err) {
    console.log('❌ Unexpected error:', err.message);
  }
}

async function runDiagnostics() {
  await checkCurrentPolicies();
  await testJWTStructure();
  await checkSupabaseJWTConfig();
  await simulateUserProfileCreation();
  
  console.log('\n🎯 Diagnosis Summary:');
  console.log('The RLS policy violation indicates that Supabase is not recognizing the Clerk JWT token.');
  console.log('This typically means:');
  console.log('1. JWT Template in Clerk is not configured correctly');
  console.log('2. Supabase JWT Secret doesn\'t match Clerk\'s signing key');
  console.log('3. JWT claims structure is incorrect');
  
  console.log('\n🔧 Next Steps:');
  console.log('1. Verify Clerk JWT Template configuration');
  console.log('2. Update Supabase JWT Secret to match Clerk');
  console.log('3. Test with a simple manual JWT token');
}

runDiagnostics().catch(console.error);
